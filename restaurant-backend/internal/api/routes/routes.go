package routes

import (
	"net/http"
	"time"

	"restaurant-backend/internal/api/handlers"
	"restaurant-backend/internal/api/middleware"
	"restaurant-backend/internal/config"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/services"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"gorm.io/gorm"
)

// SetupRoutes configures all routes for the application
func SetupRoutes(router *gin.Engine, db *gorm.DB, cfg *config.Config, logger *logrus.Logger) {
	// Initialize repositories
	userRepo := repositories.NewUserRepository(db)
	roleRepo := repositories.NewRoleRepository(db)
	menuCategoryRepo := repositories.NewMenuCategoryRepository(db)
	menuItemRepo := repositories.NewMenuItemRepository(db)
	orderRepo := repositories.NewOrderRepository(db)
	reservationRepo := repositories.NewReservationRepository(db)
	tableRepo := repositories.NewTableRepository(db)
	areaRepo := repositories.NewAreaRepository(db)
	// reviewRepo := repositories.NewReviewRepository(db) // Placeholder - to be implemented
	analyticsRepo := repositories.NewAnalyticsRepository(db)
	inventoryRepo := repositories.NewInventoryRepository(db)

	// Initialize new repositories
	serviceRepo := repositories.NewServiceRepository(db)
	shopRepo := repositories.NewShopRepository(db)
	campaignRepo := repositories.NewCampaignRepository(db)

	// Initialize services
	authService := services.NewAuthService(userRepo, cfg.JWT.Secret, cfg.JWT.ExpiresIn)
	userService := services.NewUserServiceWithDB(userRepo, roleRepo, logger, db)
	menuService := services.NewMenuService(menuCategoryRepo, menuItemRepo, logger)
	orderService := services.NewOrderService(orderRepo, menuItemRepo, tableRepo, logger)
	reservationService := services.NewReservationService(reservationRepo, logger)
	tableService := services.NewTableService(tableRepo, areaRepo, logger)
	floorService := services.NewFloorService(db)
	// reviewService := services.NewReviewService(reviewRepo, logger) // Placeholder - to be implemented
	analyticsService := services.NewAnalyticsService(analyticsRepo, orderRepo, logger)
	inventoryService := services.NewInventoryService(inventoryRepo, logger)

	// Initialize new services
	serviceService := services.NewServiceService(serviceRepo, logger)
	shopService := services.NewShopService(shopRepo, logger)
	campaignService := services.NewCampaignService(campaignRepo, logger)

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(authService, logger)
	branchHandler := handlers.NewBranchHandler(shopService, logger)
	userHandler := handlers.NewUserHandler(userService, logger)
	menuHandler := handlers.NewMenuHandler(menuService, logger)
	orderHandler := handlers.NewOrderHandler(orderService, logger)
	reservationHandler := handlers.NewReservationHandler(reservationService, logger)
	tableHandler := handlers.NewTableHandler(tableService, logger)
	floorHandler := handlers.NewFloorHandler(floorService, logger)
	// reviewHandler := handlers.NewReviewHandler(reviewService, logger) // Placeholder - to be implemented
	analyticsHandler := handlers.NewAnalyticsHandler(analyticsService, logger)
	inventoryHandler := handlers.NewInventoryHandler(inventoryService, logger)
	healthHandler := handlers.NewHealthHandler(db, logger)

	// Initialize new handlers
	serviceHandler := handlers.NewServiceHandler(serviceService, logger)
	shopHandler := handlers.NewShopHandler(shopService, logger)
	campaignHandler := handlers.NewCampaignHandler(campaignService, logger)

	// Setup middleware
	setupMiddleware(router, cfg, logger)

	// Setup API routes
	setupAPIRoutes(router, cfg,
		authHandler,
		branchHandler,
		userHandler,
		menuHandler,
		orderHandler,
		reservationHandler,
		tableHandler,
		floorHandler,
		// reviewHandler, // Placeholder - to be implemented
		analyticsHandler,
		inventoryHandler,
		healthHandler,
		serviceHandler,
		shopHandler,
		campaignHandler,
	)

	// Setup documentation
	setupDocumentation(router)
}

// setupMiddleware configures global middleware
func setupMiddleware(router *gin.Engine, cfg *config.Config, logger *logrus.Logger) {
	// Recovery middleware
	router.Use(gin.Recovery())

	// CORS middleware
	router.Use(cors.New(cors.Config{
		AllowOrigins:     cfg.CORS.AllowedOrigins,
		AllowMethods:     cfg.CORS.AllowedMethods,
		AllowHeaders:     cfg.CORS.AllowedHeaders,
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))

	// Security headers middleware
	router.Use(middleware.SecurityHeaders())

	// Request logging middleware
	router.Use(middleware.RequestLogger(logger))

	// Rate limiting middleware
	if cfg.Rate.Requests > 0 {
		router.Use(middleware.RateLimit(cfg.Rate.Requests, cfg.Rate.Window))
	}
}

// setupAPIRoutes configures all API routes
func setupAPIRoutes(
	router *gin.Engine,
	cfg *config.Config,
	authHandler *handlers.AuthHandler,
	branchHandler *handlers.BranchHandler,
	userHandler *handlers.UserHandler,
	menuHandler *handlers.MenuHandler,
	orderHandler *handlers.OrderHandler,
	reservationHandler *handlers.ReservationHandler,
	tableHandler *handlers.TableHandler,
	floorHandler *handlers.FloorHandler,
	// reviewHandler *handlers.ReviewHandler, // Placeholder - to be implemented
	analyticsHandler *handlers.AnalyticsHandler,
	inventoryHandler *handlers.InventoryHandler,
	healthHandler *handlers.HealthHandler,
	serviceHandler *handlers.ServiceHandler,
	shopHandler *handlers.ShopHandler,
	campaignHandler *handlers.CampaignHandler,
) {
	// Health check routes (no auth required)
	router.GET("/health", healthHandler.Health)
	router.GET("/ready", healthHandler.Ready)

	// API v1 routes
	v1 := router.Group("/api/v1")

	// Authentication routes (no auth required)
	auth := v1.Group("/auth")
	{
		auth.POST("/login", authHandler.Login)
		auth.POST("/register", authHandler.Register)
		auth.POST("/refresh", authHandler.RefreshToken)
		auth.POST("/logout", middleware.AuthRequired(cfg.JWT.Secret), authHandler.Logout)
		auth.GET("/me", middleware.AuthRequired(cfg.JWT.Secret), authHandler.GetCurrentUser)
	}

	// Shops routes
	shops := v1.Group("/shops")
	shops.Use(middleware.AuthRequired(cfg.JWT.Secret))
	{
		shops.GET("", shopHandler.GetShops)
		shops.POST("", shopHandler.CreateShop)
		shops.GET("/:shopId", shopHandler.GetShop)
		shops.PUT("/:shopId", shopHandler.UpdateShop)
		shops.DELETE("/:shopId", shopHandler.DeleteShop)
		shops.GET("/slug/:slug", shopHandler.GetShopBySlug)
		shops.GET("/owner/:ownerId", shopHandler.GetShopsByOwner)
		shops.GET("/type/:type", shopHandler.GetShopsByType)

		// Shop slug-based routes for dashboard and metrics
		shopSlugRoutes := shops.Group("/slug/:slug")
		{
			// Dashboard stats by slug and branch slug
			shopSlugRoutes.GET("/branches/:branchSlug/dashboard/stats", orderHandler.GetDashboardStatsBySlug)
			shopSlugRoutes.GET("/branches/:branchSlug/metrics/realtime", orderHandler.GetRealTimeMetricsBySlug)

			// Staff routes by slug and branch slug
			branchSlugRoutes := shopSlugRoutes.Group("/branches/:branchSlug")
			{
				staff := branchSlugRoutes.Group("/staff")
				{
					staff.GET("", userHandler.GetUsersBySlug)
					staff.POST("", userHandler.CreateUserBySlug)
					staff.GET("/:userId", userHandler.GetUserBySlug)
					staff.PUT("/:userId", userHandler.UpdateUserBySlug)
					staff.DELETE("/:userId", userHandler.DeleteUserBySlug)
					staff.PATCH("/:userId/status", userHandler.UpdateUserStatusBySlug)
					staff.GET("/roles", userHandler.GetRolesBySlug)
					staff.POST("/roles", userHandler.CreateRoleBySlug)
					staff.PUT("/roles/:roleId", userHandler.UpdateRoleBySlug)
					staff.DELETE("/roles/:roleId", userHandler.DeleteRoleBySlug)
					staff.GET("/permissions", userHandler.GetPermissionsBySlug)
				}
			}
		}

		// Shop branches routes
		shopBranches := shops.Group("/:shopId/branches")
		{
			shopBranches.GET("", shopHandler.GetBranches)
			shopBranches.POST("", shopHandler.CreateBranch)
			shopBranches.GET("/:branchId", shopHandler.GetBranch)
			shopBranches.PUT("/:branchId", shopHandler.UpdateBranch)
			shopBranches.DELETE("/:branchId", shopHandler.DeleteBranch)
			shopBranches.GET("/:branchId/settings", shopHandler.GetBranchSettings)
			shopBranches.PUT("/:branchId/settings", shopHandler.UpdateBranchSettings)

			// Branch-specific routes
			branch := shopBranches.Group("/:branchId")
			{
				// Floor routes
				floors := branch.Group("/floors")
				{
					floors.GET("", floorHandler.GetFloors)
					floors.POST("", floorHandler.CreateFloor)
					floors.GET("/:floorId", floorHandler.GetFloor)
					floors.PUT("/:floorId", floorHandler.UpdateFloor)
					floors.DELETE("/:floorId", floorHandler.DeleteFloor)
				}

				// Table routes
				tables := branch.Group("/tables")
				{
					tables.GET("", tableHandler.GetTables)
					tables.POST("", tableHandler.CreateTable)
					tables.GET("/:tableId", tableHandler.GetTable)
					tables.PUT("/:tableId", tableHandler.UpdateTable)
					tables.DELETE("/:tableId", tableHandler.DeleteTable)
					tables.PATCH("/:tableId/status", tableHandler.UpdateTableStatus)
					tables.POST("/:tableId/image", tableHandler.UploadTableImage)
					tables.GET("/areas", tableHandler.GetAreas)
					tables.POST("/areas", tableHandler.CreateArea)
					tables.PUT("/areas/:areaId", tableHandler.UpdateArea)
					tables.DELETE("/areas/:areaId", tableHandler.DeleteArea)
					tables.POST("/:tableId/qr-code", tableHandler.GenerateQRCode)
				}

				// Reservation routes
				reservations := branch.Group("/reservations")
				{
					reservations.GET("", reservationHandler.GetReservations)
					reservations.POST("", reservationHandler.CreateReservation)
					reservations.GET("/:reservationId", reservationHandler.GetReservation)
					reservations.PUT("/:reservationId", reservationHandler.UpdateReservation)
					reservations.DELETE("/:reservationId", reservationHandler.CancelReservation)
					reservations.POST("/:reservationId/checkin", reservationHandler.CheckInReservation)
					reservations.POST("/:reservationId/no-show", reservationHandler.MarkNoShow)
					reservations.POST("/:reservationId/cancel", reservationHandler.CancelReservation)
					reservations.GET("/today", reservationHandler.GetTodayReservations)
					reservations.GET("/availability", reservationHandler.GetAvailability)
				}

				// Menu routes
				menu := branch.Group("/menu")
				{
					// Categories
					menu.GET("/categories", menuHandler.GetCategories)
					menu.POST("/categories", menuHandler.CreateCategory)
					menu.PUT("/categories/:categoryId", menuHandler.UpdateCategory)
					menu.DELETE("/categories/:categoryId", menuHandler.DeleteCategory)

					// Items
					menu.GET("/items", menuHandler.GetMenuItems)
					menu.POST("/items", menuHandler.CreateMenuItem)
					menu.GET("/items/:itemId", menuHandler.GetMenuItem)
					menu.PUT("/items/:itemId", menuHandler.UpdateMenuItem)
					menu.DELETE("/items/:itemId", menuHandler.DeleteMenuItem)
					menu.PATCH("/items/:itemId/availability", menuHandler.ToggleAvailability)
				}

				// Order routes
				orders := branch.Group("/orders")
				{
					orders.GET("", orderHandler.GetOrders)
					orders.POST("", orderHandler.CreateOrder)
					orders.GET("/:orderId", orderHandler.GetOrder)
					orders.PUT("/:orderId", orderHandler.UpdateOrder)
					orders.PATCH("/:orderId/status", orderHandler.UpdateOrderStatus)
					orders.DELETE("/:orderId", orderHandler.DeleteOrder)
					orders.GET("/active", orderHandler.GetActiveOrders)
				}

				// Staff/User routes
				staff := branch.Group("/staff")
				{
					staff.GET("", userHandler.GetUsers)
					staff.POST("", userHandler.CreateUser)
					staff.GET("/:userId", userHandler.GetUser)
					staff.PUT("/:userId", userHandler.UpdateUser)
					staff.DELETE("/:userId", userHandler.DeleteUser)
					staff.PATCH("/:userId/status", userHandler.UpdateUserStatus)
					staff.GET("/roles", userHandler.GetRoles)
					staff.POST("/roles", userHandler.CreateRole)
					staff.PUT("/roles/:roleId", userHandler.UpdateRole)
					staff.DELETE("/roles/:roleId", userHandler.DeleteRole)
				}

				// Analytics routes
				analytics := branch.Group("/analytics")
				{
					analytics.GET("/dashboard", analyticsHandler.GetDashboard)
					analytics.GET("/sales-report", analyticsHandler.GetSalesReport)
					analytics.GET("/popular-items", analyticsHandler.GetPopularItems)
					analytics.GET("/customers", analyticsHandler.GetCustomerAnalytics)
					analytics.GET("/staff", analyticsHandler.GetStaffPerformance)
					analytics.GET("/tables", analyticsHandler.GetTableUtilization)
					analytics.POST("/export", analyticsHandler.ExportReport)
				}

				// Dashboard routes
				dashboard := branch.Group("/dashboard")
				{
					dashboard.GET("/stats", orderHandler.GetDashboardStats)
					dashboard.GET("/recent-activity", orderHandler.GetRecentActivity)
				}
			}
		}
	}

	// Merchant routes (legacy compatibility - redirects to shops)
	merchants := v1.Group("/merchants")
	merchants.Use(middleware.NextAuthRequired())
	{
		merchants.GET("", shopHandler.GetShops)
		merchants.POST("", shopHandler.CreateShop)
		merchants.GET("/:shopId", shopHandler.GetShop)
		merchants.PUT("/:shopId", shopHandler.UpdateShop)
		merchants.DELETE("/:shopId", shopHandler.DeleteShop)

		// Branch routes (updated to use shop terminology)
		branches := merchants.Group("/:shopId/branches")
		{
			branches.GET("", branchHandler.GetBranches)
			branches.POST("", branchHandler.CreateBranch)
			branches.GET("/:branchId", branchHandler.GetBranch)
			branches.PUT("/:branchId", branchHandler.UpdateBranch)
			branches.DELETE("/:branchId", branchHandler.DeleteBranch)

			// Branch-specific routes
			branch := branches.Group("/:branchId")
			{
				// Staff/User routes
				staff := branch.Group("/staff")
				{
					staff.GET("", userHandler.GetUsers)
					staff.POST("", userHandler.CreateUser)
					staff.GET("/:userId", userHandler.GetUser)
					staff.PUT("/:userId", userHandler.UpdateUser)
					staff.DELETE("/:userId", userHandler.DeleteUser)
					staff.PATCH("/:userId/status", userHandler.UpdateUserStatus)
					staff.GET("/roles", userHandler.GetRoles)
					staff.POST("/roles", userHandler.CreateRole)
					staff.PUT("/roles/:roleId", userHandler.UpdateRole)
					staff.DELETE("/roles/:roleId", userHandler.DeleteRole)
				}

				// Menu routes
				menu := branch.Group("/menu")
				{
					// Categories
					menu.GET("/categories", menuHandler.GetCategories)
					menu.POST("/categories", menuHandler.CreateCategory)
					menu.PUT("/categories/:categoryId", menuHandler.UpdateCategory)
					menu.DELETE("/categories/:categoryId", menuHandler.DeleteCategory)

					// Items
					menu.GET("/items", menuHandler.GetMenuItems)
					menu.POST("/items", menuHandler.CreateMenuItem)
					menu.GET("/items/:itemId", menuHandler.GetMenuItem)
					menu.PUT("/items/:itemId", menuHandler.UpdateMenuItem)
					menu.DELETE("/items/:itemId", menuHandler.DeleteMenuItem)
					menu.PATCH("/items/:itemId/availability", menuHandler.ToggleAvailability)
				}

				// Order routes
				orders := branch.Group("/orders")
				{
					orders.GET("", orderHandler.GetOrders)
					orders.POST("", orderHandler.CreateOrder)
					orders.GET("/:orderId", orderHandler.GetOrder)
					orders.PUT("/:orderId", orderHandler.UpdateOrder)
					orders.PATCH("/:orderId/status", orderHandler.UpdateOrderStatus)
					orders.DELETE("/:orderId", orderHandler.DeleteOrder)
					orders.GET("/active", orderHandler.GetActiveOrders)
					// orders.GET("/completed", orderHandler.GetCompletedOrders) // Use filters instead
				}

				// Reservation routes
				reservations := branch.Group("/reservations")
				{
					reservations.GET("", reservationHandler.GetReservations)
					reservations.POST("", reservationHandler.CreateReservation)
					reservations.GET("/:reservationId", reservationHandler.GetReservation)
					reservations.PUT("/:reservationId", reservationHandler.UpdateReservation)
					reservations.DELETE("/:reservationId", reservationHandler.CancelReservation)
					reservations.POST("/:reservationId/checkin", reservationHandler.CheckInReservation)
					reservations.POST("/:reservationId/no-show", reservationHandler.MarkNoShow)
					reservations.POST("/:reservationId/cancel", reservationHandler.CancelReservation)
					// reservations.POST("/:reservationId/confirm", reservationHandler.ConfirmReservation)
					// reservations.POST("/:reservationId/complete", reservationHandler.CompleteReservation)
					reservations.GET("/today", reservationHandler.GetTodayReservations)
					reservations.GET("/availability", reservationHandler.GetAvailability)
					// reservations.GET("/stats", reservationHandler.GetReservationStats)
				}

				// Table routes
				tables := branch.Group("/tables")
				{
					tables.GET("", tableHandler.GetTables)
					tables.POST("", tableHandler.CreateTable)
					tables.GET("/:tableId", tableHandler.GetTable)
					tables.PUT("/:tableId", tableHandler.UpdateTable)
					tables.DELETE("/:tableId", tableHandler.DeleteTable)
					tables.PATCH("/:tableId/status", tableHandler.UpdateTableStatus)
					tables.POST("/:tableId/image", tableHandler.UploadTableImage)
					// Layout functionality will be implemented later
					// tables.GET("/layout", tableHandler.GetLayout)
					// tables.PUT("/layout", tableHandler.UpdateLayout)
					tables.GET("/areas", tableHandler.GetAreas)
					tables.POST("/areas", tableHandler.CreateArea)
					tables.PUT("/areas/:areaId", tableHandler.UpdateArea)
					tables.DELETE("/areas/:areaId", tableHandler.DeleteArea)
					tables.POST("/:tableId/qr-code", tableHandler.GenerateQRCode)
					// tables.GET("/qr-codes", tableHandler.GetQRCodes)
					// tables.GET("/stats", tableHandler.GetTableStats)
				}

				// Review routes - Placeholder - to be implemented
				// reviews := branch.Group("/reviews")
				// {
				//	reviews.GET("", reviewHandler.GetReviews)
				//	reviews.GET("/:reviewId", reviewHandler.GetReview)
				//	reviews.POST("/:reviewId/respond", reviewHandler.RespondToReview)
				//	reviews.PUT("/:reviewId/respond", reviewHandler.UpdateResponse)
				//	reviews.DELETE("/:reviewId/respond", reviewHandler.DeleteResponse)
				//	reviews.PATCH("/:reviewId/status", reviewHandler.UpdateReviewStatus)
				//	reviews.GET("/stats", reviewHandler.GetReviewStats)
				//	reviews.GET("/recent", reviewHandler.GetRecentReviews)
				//	reviews.GET("/pending", reviewHandler.GetPendingReviews)
				// }

				// Analytics routes
				analytics := branch.Group("/analytics")
				{
					analytics.GET("/dashboard", analyticsHandler.GetDashboard)
					analytics.GET("/sales-report", analyticsHandler.GetSalesReport)
					analytics.GET("/popular-items", analyticsHandler.GetPopularItems)
					analytics.GET("/customers", analyticsHandler.GetCustomerAnalytics)
					analytics.GET("/staff", analyticsHandler.GetStaffPerformance)
					analytics.GET("/tables", analyticsHandler.GetTableUtilization)
					analytics.POST("/export", analyticsHandler.ExportReport)
				}

				// Inventory routes
				inventory := branch.Group("/inventory")
				{
					inventory.GET("/dashboard", inventoryHandler.GetDashboard)
					inventory.GET("/items", inventoryHandler.GetInventoryItems)
					inventory.PUT("/stock", inventoryHandler.UpdateStock)
					inventory.GET("/low-stock", inventoryHandler.GetLowStockItems)
					inventory.GET("/expiring", inventoryHandler.GetExpiringItems)
					inventory.GET("/movements", inventoryHandler.GetStockMovements)
					inventory.GET("/waste", inventoryHandler.GetWasteRecords)
				}

				// Reports routes
				reports := branch.Group("/reports")
				{
					reports.GET("/orders", orderHandler.GetOrderStats)
					reports.GET("/popular-items", menuHandler.GetPopularItems)
					// reports.GET("/revenue", orderHandler.GetRevenueReport) // Use order stats instead
				}

				// Dashboard routes
				dashboard := branch.Group("/dashboard")
				{
					dashboard.GET("/stats", orderHandler.GetDashboardStats)
					dashboard.GET("/recent-activity", orderHandler.GetRecentActivity)
				}
			}
		}

		// Services routes (shop-level)
		services := merchants.Group("/:shopId/services")
		{
			services.GET("", serviceHandler.GetServices)
			services.POST("", serviceHandler.CreateService)
			services.GET("/:serviceId", serviceHandler.GetService)
			services.PUT("/:serviceId", serviceHandler.UpdateService)
			services.DELETE("/:serviceId", serviceHandler.DeleteService)
			services.GET("/:serviceId/availability", serviceHandler.GetServiceAvailability)
			services.GET("/:serviceId/time-slots", serviceHandler.GetServiceTimeSlots)
		}

		// Appointments routes (shop-level)
		appointments := merchants.Group("/:shopId/appointments")
		{
			appointments.GET("", serviceHandler.GetAppointments)
			appointments.POST("", serviceHandler.CreateAppointment)
			appointments.GET("/:appointmentId", serviceHandler.GetAppointment)
			appointments.PUT("/:appointmentId", serviceHandler.UpdateAppointment)
			appointments.POST("/:appointmentId/cancel", serviceHandler.CancelAppointment)
		}

		// Staff routes (shop-level)
		staff := merchants.Group("/:shopId/staff")
		{
			staff.GET("", serviceHandler.GetStaff)
			staff.POST("", serviceHandler.CreateStaff)
			staff.GET("/:staffId", serviceHandler.GetStaffMember)
			staff.PUT("/:staffId", serviceHandler.UpdateStaff)
			staff.DELETE("/:staffId", serviceHandler.DeleteStaff)
		}

		// Campaigns routes (shop-level)
		campaigns := merchants.Group("/:shopId/campaigns")
		{
			campaigns.GET("", campaignHandler.GetCampaigns)
			campaigns.POST("", campaignHandler.CreateCampaign)
			campaigns.GET("/:campaignId", campaignHandler.GetCampaign)
			campaigns.PUT("/:campaignId", campaignHandler.UpdateCampaign)
			campaigns.DELETE("/:campaignId", campaignHandler.DeleteCampaign)
			campaigns.POST("/:campaignId/execute", campaignHandler.ExecuteCampaign)
		}

		// Communication Templates routes (shop-level)
		templates := merchants.Group("/:shopId/communication-templates")
		{
			templates.GET("", campaignHandler.GetTemplates)
			templates.POST("", campaignHandler.CreateTemplate)
			templates.GET("/:templateId", campaignHandler.GetTemplate)
			templates.PUT("/:templateId", campaignHandler.UpdateTemplate)
			templates.DELETE("/:templateId", campaignHandler.DeleteTemplate)
		}

		// Campaign Segments routes (shop-level)
		segments := merchants.Group("/:shopId/campaign-segments")
		{
			segments.GET("", campaignHandler.GetSegments)
			segments.POST("", campaignHandler.CreateSegment)
			segments.GET("/:segmentId", campaignHandler.GetSegment)
			segments.PUT("/:segmentId", campaignHandler.UpdateSegment)
			segments.DELETE("/:segmentId", campaignHandler.DeleteSegment)
			segments.GET("/:segmentId/customers", campaignHandler.GetSegmentCustomers)
			segments.GET("/:segmentId/emails", campaignHandler.GetSegmentEmails)
			segments.GET("/:segmentId/phones", campaignHandler.GetSegmentPhones)
		}

		// Communication Analytics routes (shop-level)
		commAnalytics := merchants.Group("/:shopId/communication-analytics")
		{
			commAnalytics.GET("/overview", campaignHandler.GetCommunicationAnalyticsOverview)
			commAnalytics.GET("/campaigns/:campaignId", campaignHandler.GetCampaignAnalytics)
		}
	}

	// Global ingredient and supplier routes (not branch-specific)
	v1.GET("/ingredients", inventoryHandler.GetIngredients)
	v1.POST("/ingredients", inventoryHandler.CreateIngredient)
	v1.GET("/suppliers", inventoryHandler.GetSuppliers)

	// Metrics endpoint (if enabled)
	if cfg.Metrics.Enabled {
		router.GET(cfg.Metrics.Path, gin.WrapH(middleware.PrometheusHandler()))
	}
}

// setupDocumentation configures API documentation
func setupDocumentation(router *gin.Engine) {
	// Swagger documentation
	router.GET("/docs/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// Redirect root to docs
	router.GET("/", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/docs/index.html")
	})
}
